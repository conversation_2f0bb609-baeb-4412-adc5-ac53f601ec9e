//
//  QuestionBankManagementView.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct QuestionBankManagementView: View {
    @ObservedObject var manager: QuestionBankManager
    @Binding var isPresented: Bool
    
    @State private var showingImportSheet = false
    @State private var editingBank: QuestionBank?
    @State private var newBankName = ""
    @State private var showingDeleteAlert = false
    @State private var bankToDelete: Int?
    
    var body: some View {
        NavigationView {
            VStack {
                if manager.questionBanks.isEmpty {
                    emptyStateView
                } else {
                    questionBanksList
                }
            }
            .navigationTitle("题库管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("完成") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingImportSheet = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
        }
        .sheet(isPresented: $showingImportSheet) {
            ImportSheet(isPresented: $showingImportSheet, manager: manager)
        }
        .alert("重命名题库", isPresented: .constant(editingBank != nil)) {
            TextField("题库名称", text: $newBankName)
            Button("取消", role: .cancel) {
                editingBank = nil
                newBankName = ""
            }
            Button("确定") {
                if let bank = editingBank,
                   let index = manager.questionBanks.firstIndex(where: { $0.id == bank.id }) {
                    manager.renameQuestionBank(at: index, newName: newBankName)
                }
                editingBank = nil
                newBankName = ""
            }
        } message: {
            Text("请输入新的题库名称")
        }
        .alert("删除题库", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {
                bankToDelete = nil
            }
            Button("删除", role: .destructive) {
                if let index = bankToDelete {
                    manager.removeQuestionBank(at: index)
                }
                bankToDelete = nil
            }
        } message: {
            Text("确定要删除这个题库吗？此操作无法撤销。")
        }
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "folder.badge.plus")
                .font(.system(size: 64))
                .foregroundColor(.gray)
            
            Text("暂无题库")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("点击右上角的 + 按钮导入题库文件")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button {
                showingImportSheet = true
            } label: {
                HStack {
                    Image(systemName: "plus")
                    Text("导入题库")
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
            }
        }
        .padding()
    }
    
    // MARK: - Question Banks List
    private var questionBanksList: some View {
        List {
            ForEach(Array(manager.questionBanks.enumerated()), id: \.element.id) { index, bank in
                QuestionBankManagementRow(
                    bank: bank,
                    onRename: {
                        editingBank = bank
                        newBankName = bank.name
                    },
                    onDelete: {
                        bankToDelete = index
                        showingDeleteAlert = true
                    }
                )
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Question Bank Management Row
struct QuestionBankManagementRow: View {
    let bank: QuestionBank
    let onRename: () -> Void
    let onDelete: () -> Void
    
    @State private var showingDetails = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(bank.name)
                        .font(.headline)
                        .lineLimit(1)
                    
                    Text("\(bank.questions.count) 道题目")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("创建于 \(formatDate(bank.createdAt))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(spacing: 8) {
                    Button {
                        showingDetails.toggle()
                    } label: {
                        Image(systemName: showingDetails ? "chevron.up" : "chevron.down")
                            .foregroundColor(.blue)
                    }
                    
                    Menu {
                        Button {
                            onRename()
                        } label: {
                            Label("重命名", systemImage: "pencil")
                        }
                        
                        Button(role: .destructive) {
                            onDelete()
                        } label: {
                            Label("删除", systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(.blue)
                    }
                }
            }
            
            if showingDetails {
                VStack(alignment: .leading, spacing: 8) {
                    Divider()
                    
                    Text("题目预览")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    ForEach(Array(bank.questions.prefix(3).enumerated()), id: \.element.id) { index, question in
                        VStack(alignment: .leading, spacing: 4) {
                            Text("题目 \(index + 1)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text(question.question)
                                .font(.body)
                                .lineLimit(2)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.vertical, 4)
                        
                        if index < 2 && index < bank.questions.count - 1 {
                            Divider()
                        }
                    }
                    
                    if bank.questions.count > 3 {
                        Text("还有 \(bank.questions.count - 3) 道题目...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(.vertical, 4)
        .animation(.easeInOut(duration: 0.2), value: showingDetails)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    QuestionBankManagementView(
        manager: QuestionBankManager(),
        isPresented: .constant(true)
    )
}
