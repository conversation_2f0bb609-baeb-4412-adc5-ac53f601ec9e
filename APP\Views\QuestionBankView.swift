//
//  QuestionBankView.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI
import UniformTypeIdentifiers

struct QuestionBankView: View {
    @StateObject private var manager = QuestionBankManager()
    @State private var searchText = ""
    @State private var searchResults: [(bankName: String, questions: [Question])] = []
    @State private var showingImportSheet = false
    @State private var showingManagement = false
    @State private var isSearching = false
    @FocusState private var isSearchFieldFocused: Bool
    
    @Binding var isPresented: Bool
    
    var body: some View {
        ZStack {
            // 全透明背景
            Color.clear
                .ignoresSafeArea()
                .background(.ultraThinMaterial)
            
            VStack(spacing: 0) {
                // 顶部控制栏
                topControlBar
                
                // 搜索栏
                searchBar
                
                // 内容区域
                if isSearching && !searchText.isEmpty {
                    searchResultsView
                } else {
                    quickAccessView
                }
                
                Spacer()
            }
        }
        .onTapGesture {
            // 点击空白区域隐藏键盘
            isSearchFieldFocused = false
        }
        .gesture(
            // 向下滑动关闭
            DragGesture()
                .onEnded { value in
                    if value.translation.y > 100 {
                        closeQuestionBank()
                    }
                }
        )
        .sheet(isPresented: $showingImportSheet) {
            ImportSheet(isPresented: $showingImportSheet, manager: manager)
        }
        .sheet(isPresented: $showingManagement) {
            QuestionBankManagementView(manager: manager, isPresented: $showingManagement)
        }
    }
    
    // MARK: - Top Control Bar
    private var topControlBar: some View {
        HStack {
            // 关闭按钮
            Button {
                closeQuestionBank()
            } label: {
                Image(systemName: "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.white.opacity(0.8))
            }
            
            Spacer()
            
            Text("题库助手")
                .font(.headline)
                .foregroundColor(.white)
            
            Spacer()
            
            // 管理按钮
            Button {
                showingManagement = true
            } label: {
                Image(systemName: "folder.badge.gearshape")
                    .font(.title2)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    // MARK: - Search Bar
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.white.opacity(0.6))
            
            TextField("搜索题目或答案...", text: $searchText)
                .focused($isSearchFieldFocused)
                .textFieldStyle(PlainTextFieldStyle())
                .foregroundColor(.white)
                .onSubmit {
                    performSearch()
                }
                .onChange(of: searchText) { _, newValue in
                    if newValue.isEmpty {
                        isSearching = false
                        searchResults = []
                    } else {
                        performSearch()
                    }
                }
            
            if !searchText.isEmpty {
                Button {
                    searchText = ""
                    isSearching = false
                    searchResults = []
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.white.opacity(0.6))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    // MARK: - Search Results View
    private var searchResultsView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(searchResults, id: \.bankName) { result in
                    VStack(alignment: .leading, spacing: 8) {
                        // 题库名称
                        HStack {
                            Text(result.bankName)
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Spacer()
                            
                            Text("\(result.questions.count) 题")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.6))
                        }
                        
                        // 题目列表
                        ForEach(result.questions) { question in
                            QuestionResultCard(question: question, searchKeyword: searchText)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.05))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
                            )
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
        }
    }
    
    // MARK: - Quick Access View
    private var quickAccessView: some View {
        VStack(spacing: 20) {
            // 搜索历史
            if !manager.searchHistory.isEmpty {
                searchHistorySection
            }
            
            // 题库列表
            if !manager.questionBanks.isEmpty {
                questionBanksSection
            } else {
                emptyStateView
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    // MARK: - Search History Section
    private var searchHistorySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("最近搜索")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("清除") {
                    manager.clearSearchHistory()
                }
                .font(.caption)
                .foregroundColor(.white.opacity(0.6))
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(manager.searchHistory.prefix(5), id: \.self) { keyword in
                        Button(keyword) {
                            searchText = keyword
                            performSearch()
                        }
                        .font(.caption)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.1))
                        )
                        .foregroundColor(.white.opacity(0.8))
                    }
                }
                .padding(.horizontal, 1)
            }
        }
    }
    
    // MARK: - Question Banks Section
    private var questionBanksSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("题库列表")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button {
                    showingImportSheet = true
                } label: {
                    Image(systemName: "plus.circle")
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            ForEach(Array(manager.questionBanks.enumerated()), id: \.element.id) { index, bank in
                QuestionBankCard(
                    bank: bank,
                    onTap: {
                        // 可以实现点击进入特定题库
                    }
                )
            }
        }
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.white.opacity(0.4))
            
            Text("暂无题库")
                .font(.title2)
                .foregroundColor(.white)
            
            Text("点击下方按钮导入题库文件")
                .font(.body)
                .foregroundColor(.white.opacity(0.6))
                .multilineTextAlignment(.center)
            
            Button {
                showingImportSheet = true
            } label: {
                HStack {
                    Image(systemName: "plus")
                    Text("导入题库")
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.2))
                )
                .foregroundColor(.white)
            }
        }
        .padding()
    }
    
    // MARK: - Functions
    private func performSearch() {
        guard !searchText.isEmpty else {
            isSearching = false
            searchResults = []
            return
        }
        
        isSearching = true
        searchResults = manager.searchQuestions(keyword: searchText)
    }
    
    private func closeQuestionBank() {
        isSearchFieldFocused = false
        withAnimation(.easeInOut(duration: 0.3)) {
            isPresented = false
        }
    }
}

#Preview {
    QuestionBankView(isPresented: .constant(true))
}
