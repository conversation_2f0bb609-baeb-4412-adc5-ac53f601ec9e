//
//  SidebarActivator.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct SidebarActivator: View {
    @Binding var showQuestionBank: Bool
    @State private var isVisible = false
    @State private var dragOffset: CGFloat = 0
    
    var body: some View {
        HStack {
            // 左侧激活区域
            activationArea
            
            Spacer()
        }
        .onAppear {
            // 延迟显示，避免启动时闪烁
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(.easeInOut(duration: 0.5)) {
                    isVisible = true
                }
            }
        }
    }
    
    private var activationArea: some View {
        VStack {
            Spacer()
            
            // 半透明激活按钮
            Button {
                activateQuestionBank()
            } label: {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white.opacity(0.1))
                    .frame(width: 6, height: 60)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            }
            .offset(x: dragOffset)
            .opacity(isVisible ? 1 : 0)
            .scaleEffect(isVisible ? 1 : 0.5)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isVisible)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        // 只允许向右拖拽
                        if value.translation.x > 0 {
                            dragOffset = min(value.translation.x, 100)
                        }
                    }
                    .onEnded { value in
                        if value.translation.x > 50 {
                            // 拖拽超过50点激活
                            activateQuestionBank()
                        }
                        
                        // 重置位置
                        withAnimation(.spring()) {
                            dragOffset = 0
                        }
                    }
            )
            
            Spacer()
        }
        .padding(.leading, 8)
    }
    
    private func activateQuestionBank() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 激活题库界面
        withAnimation(.easeInOut(duration: 0.3)) {
            showQuestionBank = true
        }
    }
}

// MARK: - 浮动激活按钮
struct FloatingActivator: View {
    @Binding var showQuestionBank: Bool
    @State private var position = CGPoint(x: 30, y: 100)
    @State private var isDragging = false
    @State private var opacity: Double = 0.3
    
    var body: some View {
        Circle()
            .fill(Color.white.opacity(0.1))
            .frame(width: 44, height: 44)
            .overlay(
                Circle()
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
            .overlay(
                Image(systemName: "doc.text.magnifyingglass")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.6))
            )
            .position(position)
            .opacity(opacity)
            .scaleEffect(isDragging ? 1.1 : 1.0)
            .animation(.spring(response: 0.3), value: isDragging)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        isDragging = true
                        opacity = 0.8
                        position = value.location
                    }
                    .onEnded { value in
                        isDragging = false
                        opacity = 0.3
                        
                        // 吸附到边缘
                        let screenWidth = UIScreen.main.bounds.width
                        let screenHeight = UIScreen.main.bounds.height
                        
                        // 限制在安全区域内
                        let safeX = max(30, min(screenWidth - 30, position.x))
                        let safeY = max(100, min(screenHeight - 100, position.y))
                        
                        // 吸附到最近的边缘
                        let leftDistance = safeX
                        let rightDistance = screenWidth - safeX
                        
                        withAnimation(.spring()) {
                            if leftDistance < rightDistance {
                                position = CGPoint(x: 30, y: safeY)
                            } else {
                                position = CGPoint(x: screenWidth - 30, y: safeY)
                            }
                        }
                    }
            )
            .onTapGesture {
                activateQuestionBank()
            }
            .onLongPressGesture(minimumDuration: 0.5) {
                // 长按隐藏按钮
                withAnimation(.easeOut(duration: 0.3)) {
                    opacity = 0.1
                }
                
                // 3秒后恢复
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    withAnimation(.easeIn(duration: 0.3)) {
                        opacity = 0.3
                    }
                }
            }
    }
    
    private func activateQuestionBank() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        withAnimation(.easeInOut(duration: 0.3)) {
            showQuestionBank = true
        }
    }
}

#Preview {
    ZStack {
        Color.black.ignoresSafeArea()
        
        SidebarActivator(showQuestionBank: .constant(false))
    }
}
