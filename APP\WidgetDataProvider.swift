//
//  WidgetDataProvider.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import WidgetKit
import SwiftUI

// MARK: - Widget Entry
struct SearchWidgetEntry: TimelineEntry {
    let date: Date
    let searchProvider: SearchEngine.SearchProvider
    let recentSearches: [String]
    let isHistoryEnabled: Bool
}

// MARK: - Widget Timeline Provider
struct SearchWidgetProvider: TimelineProvider {
    
    func placeholder(in context: Context) -> SearchWidgetEntry {
        SearchWidgetEntry(
            date: Date(),
            searchProvider: .google,
            recentSearches: ["示例搜索"],
            isHistoryEnabled: true
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (SearchWidgetEntry) -> Void) {
        let dataManager = DataManager()
        let entry = SearchWidgetEntry(
            date: Date(),
            searchProvider: dataManager.getSearchProvider(),
            recentSearches: Array(dataManager.getSearchHistory().prefix(3)),
            isHistoryEnabled: dataManager.getEnableHistory()
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<SearchWidgetEntry>) -> Void) {
        let dataManager = DataManager()
        let currentDate = Date()
        
        // Create entry with current data
        let entry = SearchWidgetEntry(
            date: currentDate,
            searchProvider: dataManager.getSearchProvider(),
            recentSearches: Array(dataManager.getSearchHistory().prefix(3)),
            isHistoryEnabled: dataManager.getEnableHistory()
        )
        
        // Update timeline every 15 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 15, to: currentDate) ?? currentDate
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
}

// MARK: - Widget Configuration
struct SearchWidget: Widget {
    let kind: String = "SearchWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SearchWidgetProvider()) { entry in
            SearchWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("隐蔽搜索")
        .description("快速搜索入口，支持多种搜索引擎")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

// MARK: - Widget Entry View
struct SearchWidgetEntryView: View {
    var entry: SearchWidgetProvider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(entry: entry)
        case .systemMedium:
            MediumWidgetView(entry: entry)
        default:
            SmallWidgetView(entry: entry)
        }
    }
}

// MARK: - Small Widget View
struct SmallWidgetView: View {
    let entry: SearchWidgetEntry
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: entry.searchProvider.iconName)
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Spacer()
                
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            
            Spacer()
            
            VStack(alignment: .leading, spacing: 4) {
                Text("隐蔽搜索")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(entry.searchProvider.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            HStack {
                Text("轻触搜索")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .widgetURL(URL(string: "stealthsearch://search"))
    }
}

// MARK: - Medium Widget View
struct MediumWidgetView: View {
    let entry: SearchWidgetEntry
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: entry.searchProvider.iconName)
                    .foregroundColor(.blue)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("隐蔽搜索")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("使用 \(entry.searchProvider.displayName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.white)
                    .font(.caption)
                    .padding(8)
                    .background(Color.blue)
                    .clipShape(Circle())
            }
            
            // Recent searches or empty state
            if entry.isHistoryEnabled && !entry.recentSearches.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text("最近搜索")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    
                    ForEach(entry.recentSearches.prefix(2), id: \.self) { search in
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.secondary)
                                .font(.caption2)
                            
                            Text(search)
                                .font(.caption)
                                .lineLimit(1)
                            
                            Spacer()
                        }
                        .padding(.vertical, 2)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            // Handle search tap
                        }
                    }
                }
            } else {
                HStack {
                    Text("轻触开始搜索")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .widgetURL(URL(string: "stealthsearch://search"))
    }
}


