# iOS隐蔽搜索应用开发文档

## 📱 项目概述

### 应用名称
**Stealth Search for iOS** - 隐蔽搜索助手

### 应用描述
基于iOS平台的隐蔽搜索应用，提供快速、便捷、隐蔽的搜索功能。由于iOS系统限制，采用Widget、Shortcuts等iOS原生特性替代Android的悬浮窗功能。

### 目标用户
- 需要快速搜索功能的iOS用户
- 重视隐私和便捷性的用户
- 希望提高工作效率的专业人士

## 🎯 核心功能设计

### 1. Today Widget（通知中心小组件）
**替代Android悬浮窗功能**

#### 功能特性：
- 在通知中心显示搜索入口
- 支持快速搜索输入
- 可自定义搜索引擎
- 支持搜索历史记录

#### 技术实现：
```swift
// WidgetKit实现
import WidgetKit
import SwiftUI

struct SearchWidget: Widget {
    let kind: String = "SearchWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            SearchWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("隐蔽搜索")
        .description("快速搜索入口")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}
```

### 2. Siri Shortcuts（语音快捷指令）
**语音激活搜索功能**

#### 功能特性：
- "嘿Siri，隐蔽搜索"语音激活
- 支持自定义语音指令
- 可设置快捷短语
- 支持语音输入搜索内容

#### 技术实现：
```swift
import Intents
import IntentsUI

class SearchIntentHandler: NSObject, SearchIntentHandling {
    func handle(intent: SearchIntent, completion: @escaping (SearchIntentResponse) -> Void) {
        // 处理搜索逻辑
        let response = SearchIntentResponse(code: .success, userActivity: nil)
        completion(response)
    }
}
```

### 3. App Shortcuts（应用快捷方式）
**3D Touch/长按快捷菜单**

#### 功能特性：
- 长按应用图标显示快捷菜单
- 快速搜索选项
- 搜索历史快速访问
- 设置快捷入口

### 4. Control Center Extension（控制中心扩展）
**系统控制中心集成**

#### 功能特性：
- 在控制中心添加搜索按钮
- 一键启动搜索界面
- 支持自定义图标和标题

## 🏗️ 技术架构

### 系统要求
- **iOS版本**：iOS 14.0+
- **设备支持**：iPhone 6s及以上
- **Xcode版本**：Xcode 12.0+
- **开发语言**：Swift 5.0+

### 架构设计
```
┌─────────────────────────────────────┐
│            主应用 (Main App)         │
│  ┌─────────────┐  ┌─────────────┐   │
│  │  搜索界面    │  │  设置界面    │   │
│  │  历史记录    │  │  主题配置    │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│          App Extensions             │
│  ┌─────────────┐  ┌─────────────┐   │
│  │ Today Widget │  │ Siri Intent │   │
│  │ Control Ext  │  │ App Shortcut│   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│           共享框架 (Shared)          │
│  ┌─────────────┐  ┌─────────────┐   │
│  │  搜索引擎    │  │  数据存储    │   │
│  │  网络请求    │  │  配置管理    │   │
│  └─────────────┘  └─────────────┘   │
└─────────────────────────────────────┘
```

### 核心模块

#### 1. 搜索引擎模块
```swift
// SearchEngine.swift
class SearchEngine {
    enum SearchProvider: String, CaseIterable {
        case google = "https://www.google.com/search?q="
        case bing = "https://www.bing.com/search?q="
        case duckduckgo = "https://duckduckgo.com/?q="
        case baidu = "https://www.baidu.com/s?wd="
    }
    
    static func search(query: String, provider: SearchProvider) {
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = provider.rawValue + encodedQuery
        
        if let url = URL(string: urlString) {
            UIApplication.shared.open(url)
        }
    }
}
```

#### 2. 数据存储模块
```swift
// DataManager.swift
class DataManager {
    private let userDefaults = UserDefaults(suiteName: "group.com.example.stealthsearch")
    
    // 搜索历史
    func saveSearchHistory(_ query: String) {
        var history = getSearchHistory()
        history.insert(query, at: 0)
        history = Array(history.prefix(20)) // 保留最近20条
        userDefaults?.set(history, forKey: "searchHistory")
    }
    
    func getSearchHistory() -> [String] {
        return userDefaults?.stringArray(forKey: "searchHistory") ?? []
    }
    
    // 配置管理
    func saveSearchProvider(_ provider: SearchEngine.SearchProvider) {
        userDefaults?.set(provider.rawValue, forKey: "searchProvider")
    }
    
    func getSearchProvider() -> SearchEngine.SearchProvider {
        let providerString = userDefaults?.string(forKey: "searchProvider") ?? SearchEngine.SearchProvider.google.rawValue
        return SearchEngine.SearchProvider(rawValue: providerString) ?? .google
    }
}
```

## 🎨 用户界面设计

### 1. 主应用界面

#### 搜索界面
```swift
// SearchView.swift
struct SearchView: View {
    @State private var searchText = ""
    @State private var searchHistory: [String] = []
    
    var body: some View {
        NavigationView {
            VStack {
                // 搜索输入框
                SearchBar(text: $searchText, onSearchButtonClicked: performSearch)
                
                // 搜索历史
                List(searchHistory, id: \.self) { query in
                    HStack {
                        Text(query)
                        Spacer()
                        Button("搜索") {
                            searchText = query
                            performSearch(query)
                        }
                    }
                }
            }
            .navigationTitle("隐蔽搜索")
        }
    }
    
    private func performSearch(_ query: String) {
        DataManager().saveSearchHistory(query)
        let provider = DataManager().getSearchProvider()
        SearchEngine.search(query: query, provider: provider)
    }
}
```

#### 设置界面
```swift
// SettingsView.swift
struct SettingsView: View {
    @State private var selectedProvider = SearchEngine.SearchProvider.google
    @State private var enableSiriShortcuts = true
    @State private var enableWidget = true
    
    var body: some View {
        NavigationView {
            Form {
                Section("搜索设置") {
                    Picker("搜索引擎", selection: $selectedProvider) {
                        ForEach(SearchEngine.SearchProvider.allCases, id: \.self) { provider in
                            Text(provider.displayName).tag(provider)
                        }
                    }
                }
                
                Section("功能设置") {
                    Toggle("启用Siri快捷指令", isOn: $enableSiriShortcuts)
                    Toggle("启用Widget小组件", isOn: $enableWidget)
                }
                
                Section("隐私设置") {
                    Toggle("保存搜索历史", isOn: .constant(true))
                    Button("清除搜索历史") {
                        clearSearchHistory()
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("设置")
        }
    }
}
```

### 2. Widget界面

#### Today Widget
```swift
// SearchWidget.swift
struct SearchWidgetEntryView: View {
    var entry: Provider.Entry
    
    var body: some View {
        VStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.blue)
                Text("隐蔽搜索")
                    .font(.headline)
                Spacer()
            }
            
            Spacer()
            
            HStack {
                Text("轻触开始搜索")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .widgetURL(URL(string: "stealthsearch://search"))
    }
}
```

## 🔧 开发配置

### 1. Xcode项目设置

#### App Groups配置
```xml
<!-- Entitlements -->
<key>com.apple.security.application-groups</key>
<array>
    <string>group.com.example.stealthsearch</string>
</array>
```

#### URL Schemes配置
```xml
<!-- Info.plist -->
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.example.stealthsearch</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>stealthsearch</string>
        </array>
    </dict>
</array>
```

### 2. 权限配置

#### Siri权限
```xml
<!-- Info.plist -->
<key>NSSiriUsageDescription</key>
<string>使用Siri进行语音搜索</string>
```

#### 网络权限
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

## 📦 部署与发布

### 1. 构建配置

#### Debug配置
- 开发证书签名
- 测试设备安装
- 调试日志启用

#### Release配置
- 分发证书签名
- 代码混淆启用
- 性能优化

### 2. App Store发布

#### 应用信息
- **应用名称**：隐蔽搜索助手
- **分类**：工具类
- **年龄评级**：4+
- **价格**：免费

#### 审核要点
- 功能描述清晰
- 隐私政策完整
- 用户界面友好
- 性能稳定可靠

### 3. TestFlight内测

#### 内测版本
- 功能完整性测试
- 用户体验测试
- 性能压力测试
- 兼容性测试

## 🔒 隐私与安全

### 1. 数据保护
- 搜索历史本地存储
- 不上传用户数据
- 支持数据清除

### 2. 网络安全
- HTTPS加密传输
- 证书验证
- 防中间人攻击

### 3. 隐私政策
- 明确数据使用范围
- 用户同意机制
- 数据删除权利

## 🚀 未来规划

### 版本1.0
- 基础搜索功能
- Widget支持
- Siri Shortcuts

### 版本1.1
- 搜索结果预览
- 更多搜索引擎
- 主题自定义

### 版本1.2
- iCloud同步
- Apple Watch支持
- 快捷指令自动化

## 📞 技术支持

### 开发团队联系方式
- **邮箱**：<EMAIL>
- **GitHub**：https://github.com/stealthsearch/ios
- **文档**：https://docs.stealthsearch.com

### 常见问题
1. **Widget不显示**：检查App Groups配置
2. **Siri不响应**：确认权限已授予
3. **搜索无反应**：检查网络连接

---

*本文档版本：v1.0*  
*最后更新：2024年*  
*适用于iOS 14.0+*
