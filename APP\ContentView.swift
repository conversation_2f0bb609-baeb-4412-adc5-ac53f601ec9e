//
//  ContentView.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct ContentView: View {
    @State private var showQuestionBank = false
    @State private var showingManagement = false

    var body: some View {
        ZStack {
            // 伪装界面（计算器）
            CalculatorView(showQuestionBank: $showQuestionBank)

            // 透明侧边栏激活按钮
            if !showQuestionBank {
                SidebarActivator(showQuestionBank: $showQuestionBank)
            }

            // 浮动激活按钮（备选方案）
            if !showQuestionBank {
                FloatingActivator(showQuestionBank: $showQuestionBank)
            }

            // 题库搜索界面
            if showQuestionBank {
                QuestionBankView(isPresented: $showQuestionBank)
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: showQuestionBank)
        .statusBarHidden(showQuestionBank)
    }
}

#Preview {
    ContentView()
}
