//
//  QuestionBank.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import Foundation

// MARK: - Question Model
struct Question: Identifiable, Codable {
    let id = UUID()
    let question: String
    let answer: String
    let keywords: [String] // 用于搜索优化
    
    init(question: String, answer: String) {
        self.question = question.trimmingCharacters(in: .whitespacesAndNewlines)
        self.answer = answer.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 自动提取关键词
        let questionWords = self.question.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        let answerWords = self.answer.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        self.keywords = Array(Set(questionWords + answerWords))
            .map { $0.lowercased() }
    }
}

// MARK: - Question Bank Model
struct QuestionBank: Identifiable, Codable {
    let id = UUID()
    var name: String
    var questions: [Question]
    let createdAt: Date
    var updatedAt: Date
    
    init(name: String, questions: [Question] = []) {
        self.name = name
        self.questions = questions
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    mutating func addQuestion(_ question: Question) {
        questions.append(question)
        updatedAt = Date()
    }
    
    mutating func removeQuestion(at index: Int) {
        guard index < questions.count else { return }
        questions.remove(at: index)
        updatedAt = Date()
    }
    
    mutating func updateName(_ newName: String) {
        name = newName
        updatedAt = Date()
    }
    
    // 搜索功能
    func searchQuestions(keyword: String) -> [Question] {
        guard !keyword.isEmpty else { return questions }
        
        let lowercaseKeyword = keyword.lowercased()
        
        return questions.filter { question in
            // 在题目中搜索
            question.question.lowercased().contains(lowercaseKeyword) ||
            // 在答案中搜索
            question.answer.lowercased().contains(lowercaseKeyword) ||
            // 在关键词中搜索
            question.keywords.contains { $0.contains(lowercaseKeyword) }
        }
    }
}

// MARK: - TXT Parser
struct TXTParser {
    
    enum ParseFormat {
        case questionAnswerSeparated // 题目\n答案\n\n
        case qaFormat // Q:题目\nA:答案
        case colonSeparated // 题目:答案
        case autoDetect
    }
    
    static func parseQuestions(from content: String, format: ParseFormat = .autoDetect) -> [Question] {
        let detectedFormat = format == .autoDetect ? detectFormat(content) : format
        
        switch detectedFormat {
        case .questionAnswerSeparated:
            return parseQuestionAnswerSeparated(content)
        case .qaFormat:
            return parseQAFormat(content)
        case .colonSeparated:
            return parseColonSeparated(content)
        case .autoDetect:
            return parseQuestionAnswerSeparated(content) // 默认格式
        }
    }
    
    private static func detectFormat(_ content: String) -> ParseFormat {
        let lines = content.components(separatedBy: .newlines)
        
        // 检测 Q: A: 格式
        let qaCount = lines.filter { $0.hasPrefix("Q:") || $0.hasPrefix("A:") }.count
        if qaCount > 0 {
            return .qaFormat
        }
        
        // 检测冒号分隔格式
        let colonCount = lines.filter { $0.contains(":") && !$0.hasPrefix("Q:") && !$0.hasPrefix("A:") }.count
        if colonCount > lines.count / 3 {
            return .colonSeparated
        }
        
        // 默认使用题目答案分隔格式
        return .questionAnswerSeparated
    }
    
    private static func parseQuestionAnswerSeparated(_ content: String) -> [Question] {
        var questions: [Question] = []
        
        // 按双换行符分割题目块
        let questionBlocks = content.components(separatedBy: "\n\n")
        
        for block in questionBlocks {
            let lines = block.components(separatedBy: .newlines)
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
            
            if lines.count >= 2 {
                let question = lines[0]
                let answer = lines[1..<lines.count].joined(separator: "\n")
                
                if !question.isEmpty && !answer.isEmpty {
                    questions.append(Question(question: question, answer: answer))
                }
            }
        }
        
        return questions
    }
    
    private static func parseQAFormat(_ content: String) -> [Question] {
        var questions: [Question] = []
        let lines = content.components(separatedBy: .newlines)
        
        var currentQuestion = ""
        var currentAnswer = ""
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if trimmedLine.hasPrefix("Q:") {
                // 保存上一个题目
                if !currentQuestion.isEmpty && !currentAnswer.isEmpty {
                    questions.append(Question(question: currentQuestion, answer: currentAnswer))
                }
                
                currentQuestion = String(trimmedLine.dropFirst(2)).trimmingCharacters(in: .whitespacesAndNewlines)
                currentAnswer = ""
            } else if trimmedLine.hasPrefix("A:") {
                currentAnswer = String(trimmedLine.dropFirst(2)).trimmingCharacters(in: .whitespacesAndNewlines)
            } else if !trimmedLine.isEmpty {
                // 多行答案
                if !currentAnswer.isEmpty {
                    currentAnswer += "\n" + trimmedLine
                } else if !currentQuestion.isEmpty {
                    currentQuestion += "\n" + trimmedLine
                }
            }
        }
        
        // 保存最后一个题目
        if !currentQuestion.isEmpty && !currentAnswer.isEmpty {
            questions.append(Question(question: currentQuestion, answer: currentAnswer))
        }
        
        return questions
    }
    
    private static func parseColonSeparated(_ content: String) -> [Question] {
        var questions: [Question] = []
        let lines = content.components(separatedBy: .newlines)
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedLine.isEmpty else { continue }
            
            let components = trimmedLine.components(separatedBy: ":")
            if components.count >= 2 {
                let question = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                let answer = components[1..<components.count]
                    .joined(separator: ":")
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                
                if !question.isEmpty && !answer.isEmpty {
                    questions.append(Question(question: question, answer: answer))
                }
            }
        }
        
        return questions
    }
}
