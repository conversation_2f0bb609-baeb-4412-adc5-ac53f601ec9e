//
//  QuestionCards.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

// MARK: - Question Result Card
struct QuestionResultCard: View {
    let question: Question
    let searchKeyword: String
    @State private var showAnswer = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 题目
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text("题目")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Spacer()
                    
                    Button {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            showAnswer.toggle()
                        }
                    } label: {
                        HStack(spacing: 4) {
                            Text(showAnswer ? "隐藏答案" : "显示答案")
                            Image(systemName: showAnswer ? "eye.slash" : "eye")
                        }
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    }
                }
                
                Text(highlightedText(question.question, keyword: searchKeyword))
                    .font(.body)
                    .foregroundColor(.white)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            // 答案
            if showAnswer {
                VStack(alignment: .leading, spacing: 6) {
                    Text("答案")
                        .font(.caption)
                        .foregroundColor(.green.opacity(0.8))
                    
                    Text(highlightedText(question.answer, keyword: searchKeyword))
                        .font(.body)
                        .foregroundColor(.green)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                showAnswer.toggle()
            }
        }
    }
    
    private func highlightedText(_ text: String, keyword: String) -> AttributedString {
        var attributedString = AttributedString(text)
        
        if !keyword.isEmpty {
            let lowercaseText = text.lowercased()
            let lowercaseKeyword = keyword.lowercased()
            
            var searchRange = lowercaseText.startIndex
            
            while let range = lowercaseText.range(of: lowercaseKeyword, range: searchRange..<lowercaseText.endIndex) {
                let attributedRange = Range(range, in: attributedString)!
                attributedString[attributedRange].backgroundColor = .yellow.opacity(0.3)
                attributedString[attributedRange].foregroundColor = .yellow
                
                searchRange = range.upperBound
            }
        }
        
        return attributedString
    }
}

// MARK: - Question Bank Card
struct QuestionBankCard: View {
    let bank: QuestionBank
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(bank.name)
                        .font(.headline)
                        .foregroundColor(.white)
                        .lineLimit(1)
                    
                    Text("\(bank.questions.count) 道题目")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Text("更新于 \(formatDate(bank.updatedAt))")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.4))
                }
                
                Spacer()
                
                VStack {
                    Image(systemName: "doc.text")
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Spacer()
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.08))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.15), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Import Sheet
struct ImportSheet: View {
    @Binding var isPresented: Bool
    @ObservedObject var manager: QuestionBankManager
    @State private var bankName = ""
    @State private var showingFilePicker = false
    @State private var importError: ImportError?
    @State private var showingError = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 标题
                VStack(spacing: 8) {
                    Image(systemName: "doc.badge.plus")
                        .font(.system(size: 48))
                        .foregroundColor(.blue)
                    
                    Text("导入题库")
                        .font(.title)
                        .fontWeight(.semibold)
                    
                    Text("支持 TXT 格式的题库文件")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                
                // 题库名称输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("题库名称")
                        .font(.headline)
                    
                    TextField("请输入题库名称", text: $bankName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // 格式说明
                VStack(alignment: .leading, spacing: 12) {
                    Text("支持的格式")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        FormatExample(
                            title: "格式一：题目答案分隔",
                            example: "题目内容\n答案内容\n\n下一题..."
                        )
                        
                        FormatExample(
                            title: "格式二：Q&A格式",
                            example: "Q:题目内容\nA:答案内容"
                        )
                        
                        FormatExample(
                            title: "格式三：冒号分隔",
                            example: "题目内容:答案内容"
                        )
                    }
                }
                
                Spacer()
                
                // 导入按钮
                Button {
                    if !bankName.isEmpty {
                        showingFilePicker = true
                    }
                } label: {
                    HStack {
                        Image(systemName: "folder")
                        Text("选择文件")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(bankName.isEmpty ? Color.gray : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(bankName.isEmpty)
            }
            .padding()
            .navigationTitle("导入题库")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        isPresented = false
                    }
                }
            }
        }
        .fileImporter(
            isPresented: $showingFilePicker,
            allowedContentTypes: [UTType.plainText],
            allowsMultipleSelection: false
        ) { result in
            handleFileImport(result)
        }
        .alert("导入错误", isPresented: $showingError) {
            Button("确定") { }
        } message: {
            Text(importError?.localizedDescription ?? "未知错误")
        }
    }
    
    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            
            do {
                try manager.importQuestionBank(from: url, name: bankName)
                isPresented = false
            } catch let error as ImportError {
                importError = error
                showingError = true
            } catch {
                importError = .invalidFormat
                showingError = true
            }
            
        case .failure:
            importError = .accessDenied
            showingError = true
        }
    }
}

// MARK: - Format Example
struct FormatExample: View {
    let title: String
    let example: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Text(example)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(8)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(6)
        }
    }
}

#Preview {
    QuestionResultCard(
        question: Question(
            question: "什么是SwiftUI？",
            answer: "SwiftUI是苹果公司推出的用户界面工具包"
        ),
        searchKeyword: "SwiftUI"
    )
    .padding()
    .background(Color.black)
}
