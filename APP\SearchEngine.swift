//
//  SearchEngine.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import UIKit

class SearchEngine {
    enum SearchProvider: String, CaseIterable {
        case google = "https://www.google.com/search?q="
        case bing = "https://www.bing.com/search?q="
        case duckduckgo = "https://duckduckgo.com/?q="
        case baidu = "https://www.baidu.com/s?wd="
        
        var displayName: String {
            switch self {
            case .google:
                return "Google"
            case .bing:
                return "Bing"
            case .duckduckgo:
                return "DuckDuckGo"
            case .baidu:
                return "百度"
            }
        }
        
        var iconName: String {
            switch self {
            case .google:
                return "magnifyingglass"
            case .bing:
                return "magnifyingglass.circle"
            case .duckduckgo:
                return "shield"
            case .baidu:
                return "globe.asia.australia"
            }
        }
    }
    
    static func search(query: String, provider: SearchProvider) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = provider.rawValue + encodedQuery
        
        if let url = URL(string: urlString) {
            UIApplication.shared.open(url)
        }
    }
    
    static func canOpenURL(_ provider: SearchProvider) -> Bool {
        let testQuery = "test"
        let encodedQuery = testQuery.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = provider.rawValue + encodedQuery
        
        if let url = URL(string: urlString) {
            return UIApplication.shared.canOpenURL(url)
        }
        return false
    }
}
