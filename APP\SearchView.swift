//
//  SearchView.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct SearchView: View {
    @StateObject private var dataManager = DataManager()
    @StateObject private var siriManager = SiriShortcutsManager()
    @State private var searchText = ""
    @State private var showingSettings = false
    @FocusState private var isSearchFieldFocused: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Header
                searchHeader
                
                // Search Input
                searchInputSection
                
                // Search History
                if dataManager.enableHistory && !dataManager.searchHistory.isEmpty {
                    searchHistorySection
                        .transition(.opacity.combined(with: .move(edge: .top)))
                } else {
                    emptyStateView
                        .transition(.opacity.combined(with: .scale))
                }
                
                Spacer()
            }
            .navigationTitle("隐蔽搜索")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingSettings = true
                    } label: {
                        Image(systemName: "gearshape")
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                SettingsView(dataManager: dataManager, siriManager: siriManager)
            }
            .onAppear {
                // Donate search activity to Siri when view appears
                siriManager.donateSearchActivity()
            }
        }
    }
    
    // MARK: - Search Header
    private var searchHeader: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: dataManager.selectedProvider.iconName)
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("使用 \(dataManager.selectedProvider.displayName) 搜索")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            .padding(.horizontal)
        }
        .padding(.top, 8)
    }
    
    // MARK: - Search Input Section
    private var searchInputSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("输入搜索内容...", text: $searchText)
                    .focused($isSearchFieldFocused)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onSubmit {
                        performSearch(searchText)
                    }
                
                if !searchText.isEmpty {
                    Button {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            searchText = ""
                        }
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .transition(.scale.combined(with: .opacity))
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .padding(.horizontal)
            
            // Search Button
            Button {
                performSearch(searchText)
            } label: {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .font(.body.weight(.medium))
                    Text("搜索")
                        .font(.body.weight(.medium))
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(searchText.isEmpty ? Color.gray : Color.blue)
                        .animation(.easeInOut(duration: 0.2), value: searchText.isEmpty)
                )
                .foregroundColor(.white)
                .scaleEffect(searchText.isEmpty ? 0.98 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: searchText.isEmpty)
            }
            .disabled(searchText.isEmpty)
            .padding(.horizontal)
        }
        .padding(.vertical)
    }
    
    // MARK: - Search History Section
    private var searchHistorySection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("搜索历史")
                    .font(.headline)
                    .padding(.horizontal)
                
                Spacer()
                
                Button("清除") {
                    dataManager.clearSearchHistory()
                }
                .font(.caption)
                .foregroundColor(.red)
                .padding(.horizontal)
            }
            
            List {
                ForEach(dataManager.searchHistory, id: \.self) { query in
                    SearchHistoryRow(
                        query: query,
                        onTap: {
                            searchText = query
                            performSearch(query)
                        },
                        onDelete: {
                            dataManager.removeSearchHistoryItem(query)
                        }
                    )
                }
            }
            .listStyle(PlainListStyle())
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.6))

            VStack(spacing: 8) {
                Text("开始搜索")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("输入关键词进行搜索\n搜索历史将显示在这里")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }

            // Quick action buttons
            VStack(spacing: 12) {
                Text("快速搜索")
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack(spacing: 12) {
                    ForEach(["iOS开发", "SwiftUI", "Widget"], id: \.self) { suggestion in
                        Button(suggestion) {
                            searchText = suggestion
                            performSearch(suggestion)
                        }
                        .font(.caption)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.blue.opacity(0.1))
                        )
                        .foregroundColor(.blue)
                    }
                }
            }
            .padding(.top, 8)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Search Function
    private func performSearch(_ query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return }

        // Save to history
        dataManager.saveSearchHistory(trimmedQuery)

        // Donate to Siri for future shortcuts
        siriManager.donateQuickSearchActivity(query: trimmedQuery)

        // Perform search
        SearchEngine.search(query: trimmedQuery, provider: dataManager.selectedProvider)

        // Clear search field and dismiss keyboard
        searchText = ""
        isSearchFieldFocused = false
    }
}

// MARK: - Search History Row
struct SearchHistoryRow: View {
    let query: String
    let onTap: () -> Void
    let onDelete: () -> Void
    @State private var isPressed = false

    var body: some View {
        HStack {
            Image(systemName: "clock")
                .foregroundColor(.secondary)
                .font(.caption)

            Text(query)
                .lineLimit(1)
                .font(.body)

            Spacer()

            Button {
                withAnimation(.easeInOut(duration: 0.1)) {
                    onTap()
                }
            } label: {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.blue)
                    .font(.body)
                    .padding(8)
                    .background(
                        Circle()
                            .fill(Color.blue.opacity(0.1))
                            .opacity(isPressed ? 1 : 0)
                    )
            }
            .buttonStyle(PlainButtonStyle())
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .onLongPressGesture(minimumDuration: 0) {
                // Handle press
            } onPressingChanged: { pressing in
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = pressing
                }
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                onTap()
            }
        }
        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
            Button("删除", role: .destructive) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    onDelete()
                }
            }
        }
    }
}

#Preview {
    SearchView()
}
