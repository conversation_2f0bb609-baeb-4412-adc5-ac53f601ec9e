//
//  SettingsView.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct SettingsView: View {
    @ObservedObject var dataManager: DataManager
    @ObservedObject var siriManager: SiriShortcutsManager
    @Environment(\.dismiss) private var dismiss
    @State private var showingClearAlert = false
    @State private var showingSiriSuccess = false
    
    var body: some View {
        NavigationView {
            Form {
                // Search Engine Section
                searchEngineSection
                
                // History Settings Section
                historySettingsSection

                // Siri Shortcuts Section
                if siriManager.isShortcutsAvailable {
                    siriShortcutsSection
                }

                // Privacy Section
                privacySection
                
                // About Section
                aboutSection
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .alert("清除搜索历史", isPresented: $showingClearAlert) {
            Button("取消", role: .cancel) { }
            Button("清除", role: .destructive) {
                dataManager.clearSearchHistory()
            }
        } message: {
            Text("确定要清除所有搜索历史吗？此操作无法撤销。")
        }
        .alert("Siri 快捷指令", isPresented: $showingSiriSuccess) {
            Button("好的") { }
        } message: {
            Text("已成功添加到 Siri 快捷指令！现在你可以说\"嘿Siri，隐蔽搜索\"来快速搜索。")
        }
    }
    
    // MARK: - Search Engine Section
    private var searchEngineSection: some View {
        Section {
            ForEach(SearchEngine.SearchProvider.allCases, id: \.self) { provider in
                HStack {
                    Image(systemName: provider.iconName)
                        .foregroundColor(.blue)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(provider.displayName)
                            .font(.body)
                        
                        Text(provider.rawValue)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if dataManager.selectedProvider == provider {
                        Image(systemName: "checkmark")
                            .foregroundColor(.blue)
                            .font(.body.weight(.semibold))
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    dataManager.saveSearchProvider(provider)
                }
            }
        } header: {
            Text("搜索引擎")
        } footer: {
            Text("选择默认的搜索引擎")
        }
    }
    
    // MARK: - History Settings Section
    private var historySettingsSection: some View {
        Section {
            Toggle("保存搜索历史", isOn: Binding(
                get: { dataManager.enableHistory },
                set: { dataManager.setEnableHistory($0) }
            ))
            
            if dataManager.enableHistory {
                HStack {
                    Text("历史记录数量")
                    Spacer()
                    Text("\(dataManager.searchHistory.count) 条")
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("最大保存数量")
                    Spacer()
                    Text("\(dataManager.getMaxHistoryCount()) 条")
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("搜索历史")
        } footer: {
            if dataManager.enableHistory {
                Text("搜索历史将保存在本地设备上，不会上传到服务器")
            } else {
                Text("关闭后将不再保存新的搜索历史")
            }
        }
    }

    // MARK: - Siri Shortcuts Section
    private var siriShortcutsSection: some View {
        Section {
            Button {
                addSearchShortcutToSiri()
            } label: {
                HStack {
                    Image(systemName: "mic")
                        .foregroundColor(.blue)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("添加到 Siri")
                            .font(.body)
                        Text("说\"嘿Siri，隐蔽搜索\"")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                    Image(systemName: "plus")
                        .foregroundColor(.blue)
                        .font(.caption)
                }
            }
            .buttonStyle(PlainButtonStyle())

            HStack {
                Image(systemName: "waveform")
                    .foregroundColor(.green)
                VStack(alignment: .leading, spacing: 2) {
                    Text("语音搜索")
                        .font(.body)
                    Text("支持语音输入搜索内容")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("Siri 快捷指令")
        } footer: {
            Text("添加快捷指令后，你可以通过语音快速启动搜索功能")
        }
    }

    // MARK: - Privacy Section
    private var privacySection: some View {
        Section {
            Button {
                showingClearAlert = true
            } label: {
                HStack {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                    Text("清除搜索历史")
                        .foregroundColor(.red)
                }
            }
            .disabled(dataManager.searchHistory.isEmpty)
            
            HStack {
                Image(systemName: "shield")
                    .foregroundColor(.green)
                VStack(alignment: .leading, spacing: 2) {
                    Text("隐私保护")
                        .font(.body)
                    Text("所有数据仅存储在本地")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        } header: {
            Text("隐私设置")
        } footer: {
            Text("应用不会收集或上传任何个人数据，所有搜索记录仅保存在您的设备上")
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        Section {
            HStack {
                Text("应用版本")
                Spacer()
                Text("1.0.0")
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("开发者")
                Spacer()
                Text("tongsy")
                    .foregroundColor(.secondary)
            }
            
            Link(destination: URL(string: "https://github.com/stealthsearch/ios")!) {
                HStack {
                    Image(systemName: "link")
                        .foregroundColor(.blue)
                    Text("GitHub 项目")
                    Spacer()
                    Image(systemName: "arrow.up.right")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }
        } header: {
            Text("关于")
        }
    }

    // MARK: - Siri Functions
    private func addSearchShortcutToSiri() {
        if #available(iOS 12.0, *) {
            let activity = StealthSearchIntentHandler.createSearchActivity()
            siriManager.addShortcutToSiri(for: activity) { success in
                if success {
                    showingSiriSuccess = true
                }
            }
        }
    }
}

#Preview {
    SettingsView(dataManager: DataManager(), siriManager: SiriShortcutsManager())
}
