//
//  SearchIntents.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import Intents
import IntentsUI

// MARK: - Search Intent Definition
@available(iOS 12.0, *)
class SearchIntent: INIntent {
    
}

// MARK: - Search Intent Handler
@available(iOS 12.0, *)
class SearchIntentHandler: NSObject, INSearchForMessagesIntentHandling {
    
    func handle(intent: INSearchForMessagesIntent, completion: @escaping (INSearchForMessagesIntentResponse) -> Void) {
        // This is a placeholder - we'll use a custom intent instead
        let response = INSearchForMessagesIntentResponse(code: .success, userActivity: nil)
        completion(response)
    }
}

// MARK: - Custom Search Intent using NSUserActivity
class StealthSearchIntentHandler: NSObject {
    
    static let searchActivityType = "com.label.stealthsearch.search"
    static let quickSearchActivityType = "com.label.stealthsearch.quicksearch"
    
    // Create user activity for Siri Shortcuts
    static func createSearchActivity() -> NSUserActivity {
        let activity = NSUserActivity(activityType: searchActivityType)
        activity.title = "隐蔽搜索"
        activity.suggestedInvocationPhrase = "隐蔽搜索"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "search"
        
        // Add user info for handling
        activity.userInfo = [
            "action": "search",
            "provider": DataManager().getSearchProvider().rawValue
        ]
        
        return activity
    }
    
    // Create quick search activity with query
    static func createQuickSearchActivity(query: String) -> NSUserActivity {
        let activity = NSUserActivity(activityType: quickSearchActivityType)
        activity.title = "搜索：\(query)"
        activity.suggestedInvocationPhrase = "搜索\(query)"
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "quicksearch_\(query)"
        
        // Add user info for handling
        activity.userInfo = [
            "action": "quicksearch",
            "query": query,
            "provider": DataManager().getSearchProvider().rawValue
        ]
        
        return activity
    }
    
    // Handle user activity
    static func handleUserActivity(_ userActivity: NSUserActivity) -> Bool {
        guard let userInfo = userActivity.userInfo,
              let action = userInfo["action"] as? String else {
            return false
        }
        
        switch action {
        case "search":
            // Open app to search view - handled by main app
            return true
            
        case "quicksearch":
            if let query = userInfo["query"] as? String,
               let providerString = userInfo["provider"] as? String,
               let provider = SearchEngine.SearchProvider(rawValue: providerString) {
                
                // Perform search directly
                SearchEngine.search(query: query, provider: provider)
                
                // Save to history
                DataManager().saveSearchHistory(query)
                
                return true
            }
            return false
            
        default:
            return false
        }
    }
}

// MARK: - Siri Shortcuts Manager
class SiriShortcutsManager: ObservableObject {
    
    @Published var isShortcutsAvailable = false
    
    init() {
        checkShortcutsAvailability()
    }
    
    private func checkShortcutsAvailability() {
        if #available(iOS 12.0, *) {
            isShortcutsAvailable = true
        } else {
            isShortcutsAvailable = false
        }
    }
    
    // Donate search activity to Siri
    func donateSearchActivity() {
        guard isShortcutsAvailable else { return }
        
        let activity = StealthSearchIntentHandler.createSearchActivity()
        activity.becomeCurrent()
    }
    
    // Donate quick search activity
    func donateQuickSearchActivity(query: String) {
        guard isShortcutsAvailable, !query.isEmpty else { return }
        
        let activity = StealthSearchIntentHandler.createQuickSearchActivity(query: query)
        activity.becomeCurrent()
    }
    
    // Add shortcut to Siri (iOS 12+)
    @available(iOS 12.0, *)
    func addShortcutToSiri(for activity: NSUserActivity, completion: @escaping (Bool) -> Void) {
        let shortcut = INShortcut(userActivity: activity)
        
        let viewController = INUIAddVoiceShortcutViewController(shortcut: shortcut)
        viewController.delegate = SiriShortcutDelegate { success in
            DispatchQueue.main.async {
                completion(success)
            }
        }
        
        // Present the view controller
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(viewController, animated: true)
        }
    }
}

// MARK: - Siri Shortcut Delegate
@available(iOS 12.0, *)
class SiriShortcutDelegate: NSObject, INUIAddVoiceShortcutViewControllerDelegate {
    
    private let completion: (Bool) -> Void
    
    init(completion: @escaping (Bool) -> Void) {
        self.completion = completion
    }
    
    func addVoiceShortcutViewController(_ controller: INUIAddVoiceShortcutViewController, didFinishWith voiceShortcut: INVoiceShortcut?, error: Error?) {
        controller.dismiss(animated: true) {
            self.completion(voiceShortcut != nil && error == nil)
        }
    }
    
    func addVoiceShortcutViewControllerDidCancel(_ controller: INUIAddVoiceShortcutViewController) {
        controller.dismiss(animated: true) {
            self.completion(false)
        }
    }
}
