//
//  CalculatorView.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

struct CalculatorView: View {
    @State private var display = "0"
    @State private var previousValue = 0.0
    @State private var operation: Operation?
    @State private var waitingForOperand = false
    @State private var secretTapCount = 0
    @State private var lastTapTime = Date()
    
    @Binding var showQuestionBank: Bool
    
    enum Operation {
        case add, subtract, multiply, divide, equals
    }
    
    var body: some View {
        VStack(spacing: 1) {
            // Display
            displayView
            
            // Button Grid
            buttonGrid
        }
        .background(Color.black)
        .onTapGesture(count: 2) {
            handleSecretGesture()
        }
    }
    
    // MARK: - Display View
    private var displayView: some View {
        HStack {
            Spacer()
            Text(display)
                .font(.system(size: 64, weight: .light))
                .foregroundColor(.white)
                .lineLimit(1)
                .minimumScaleFactor(0.5)
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 40)
        .frame(maxHeight: .infinity)
        .background(Color.black)
    }
    
    // MARK: - Button Grid
    private var buttonGrid: some View {
        VStack(spacing: 1) {
            // Row 1: AC, +/-, %, ÷
            HStack(spacing: 1) {
                CalculatorButton(title: "AC", color: .gray, action: { clear() })
                CalculatorButton(title: "±", color: .gray, action: { toggleSign() })
                CalculatorButton(title: "%", color: .gray, action: { percent() })
                CalculatorButton(title: "÷", color: .orange, action: { setOperation(.divide) })
            }
            
            // Row 2: 7, 8, 9, ×
            HStack(spacing: 1) {
                CalculatorButton(title: "7", color: .darkGray, action: { inputDigit("7") })
                CalculatorButton(title: "8", color: .darkGray, action: { inputDigit("8") })
                CalculatorButton(title: "9", color: .darkGray, action: { inputDigit("9") })
                CalculatorButton(title: "×", color: .orange, action: { setOperation(.multiply) })
            }
            
            // Row 3: 4, 5, 6, -
            HStack(spacing: 1) {
                CalculatorButton(title: "4", color: .darkGray, action: { inputDigit("4") })
                CalculatorButton(title: "5", color: .darkGray, action: { inputDigit("5") })
                CalculatorButton(title: "6", color: .darkGray, action: { inputDigit("6") })
                CalculatorButton(title: "-", color: .orange, action: { setOperation(.subtract) })
            }
            
            // Row 4: 1, 2, 3, +
            HStack(spacing: 1) {
                CalculatorButton(title: "1", color: .darkGray, action: { inputDigit("1") })
                CalculatorButton(title: "2", color: .darkGray, action: { inputDigit("2") })
                CalculatorButton(title: "3", color: .darkGray, action: { inputDigit("3") })
                CalculatorButton(title: "+", color: .orange, action: { setOperation(.add) })
            }
            
            // Row 5: 0, ., =
            HStack(spacing: 1) {
                // 宽按钮 0
                Button(action: { inputDigit("0") }) {
                    Text("0")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.darkGray)
                }
                .frame(height: 80)
                .frame(maxWidth: .infinity)

                CalculatorButton(title: ".", color: .darkGray, action: { inputDecimal() })
                CalculatorButton(title: "=", color: .orange, action: { calculate() })
            }
        }
    }
    
    // MARK: - Calculator Functions
    private func inputDigit(_ digit: String) {
        if waitingForOperand {
            display = digit
            waitingForOperand = false
        } else {
            display = display == "0" ? digit : display + digit
        }
    }
    
    private func inputDecimal() {
        if waitingForOperand {
            display = "0."
            waitingForOperand = false
        } else if !display.contains(".") {
            display += "."
        }
    }
    
    private func clear() {
        display = "0"
        previousValue = 0
        operation = nil
        waitingForOperand = false
    }
    
    private func toggleSign() {
        if display != "0" {
            if display.hasPrefix("-") {
                display = String(display.dropFirst())
            } else {
                display = "-" + display
            }
        }
    }
    
    private func percent() {
        if let value = Double(display) {
            display = String(value / 100)
        }
    }
    
    private func setOperation(_ op: Operation) {
        if let value = Double(display) {
            if operation != nil && !waitingForOperand {
                calculate()
            } else {
                previousValue = value
            }
            
            operation = op
            waitingForOperand = true
        }
    }
    
    private func calculate() {
        guard let op = operation, let currentValue = Double(display) else { return }
        
        var result: Double
        
        switch op {
        case .add:
            result = previousValue + currentValue
        case .subtract:
            result = previousValue - currentValue
        case .multiply:
            result = previousValue * currentValue
        case .divide:
            result = currentValue != 0 ? previousValue / currentValue : 0
        case .equals:
            result = currentValue
        }
        
        display = formatResult(result)
        operation = nil
        waitingForOperand = true
        previousValue = result
    }
    
    private func formatResult(_ value: Double) -> String {
        if value.truncatingRemainder(dividingBy: 1) == 0 {
            return String(Int(value))
        } else {
            return String(value)
        }
    }
    
    // MARK: - Secret Gesture
    private func handleSecretGesture() {
        let now = Date()
        
        // 检查是否在2秒内
        if now.timeIntervalSince(lastTapTime) < 2.0 {
            secretTapCount += 1
        } else {
            secretTapCount = 1
        }
        
        lastTapTime = now
        
        // 连续双击3次进入题库
        if secretTapCount >= 3 {
            withAnimation(.easeInOut(duration: 0.3)) {
                showQuestionBank = true
            }
            secretTapCount = 0
        }
    }
}

// MARK: - Calculator Button
struct CalculatorButton: View {
    let title: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(color == .orange ? .white : (color == .gray ? .black : .white))
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(color)
        }
        .frame(height: 80)
    }
}

// MARK: - Color Extensions
extension Color {
    static let darkGray = Color(red: 0.2, green: 0.2, blue: 0.2)
    static let gray = Color(red: 0.6, green: 0.6, blue: 0.6)
}

#Preview {
    CalculatorView(showQuestionBank: .constant(false))
}
