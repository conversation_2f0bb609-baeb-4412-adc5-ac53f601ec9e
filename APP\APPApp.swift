//
//  APPApp.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI

@main
struct APPApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .onOpenURL { url in
                    handleURL(url)
                }
                .onContinueUserActivity(StealthSearchIntentHandler.searchActivityType) { userActivity in
                    _ = StealthSearchIntentHandler.handleUserActivity(userActivity)
                }
                .onContinueUserActivity(StealthSearchIntentHandler.quickSearchActivityType) { userActivity in
                    _ = StealthSearchIntentHandler.handleUserActivity(userActivity)
                }
        }
    }

    private func handleURL(_ url: URL) {
        // Handle URL schemes from Widget
        if url.scheme == "stealthsearch" {
            switch url.host {
            case "search":
                // Widget tapped - app will open to search view
                // The ContentView already shows SearchView, so no additional action needed
                break
            default:
                break
            }
        }
    }
}
