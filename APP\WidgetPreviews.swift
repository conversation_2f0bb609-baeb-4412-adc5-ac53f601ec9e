//
//  WidgetPreviews.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI
import WidgetKit

struct WidgetPreviews: PreviewProvider {
    static var previews: some View {
        Group {
            SearchWidgetEntryView(entry: SearchWidgetEntry(
                date: Date(),
                searchProvider: .google,
                recentSearches: ["iOS开发", "SwiftUI"],
                isHistoryEnabled: true
            ))
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            .previewDisplayName("Small Widget")

            SearchWidgetEntryView(entry: SearchWidgetEntry(
                date: Date(),
                searchProvider: .google,
                recentSearches: ["iOS开发", "SwiftUI", "Widget"],
                isHistoryEnabled: true
            ))
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            .previewDisplayName("Medium Widget")
        }
    }
}
