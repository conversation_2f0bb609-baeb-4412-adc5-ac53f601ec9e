//
//  WidgetPreviews.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import SwiftUI
import WidgetKit

#Preview("Small Widget", as: .systemSmall) {
    SearchWidget()
} timeline: {
    SearchWidgetEntry(
        date: Date(),
        searchProvider: .google,
        recentSearches: ["iOS开发", "SwiftUI"],
        isHistoryEnabled: true
    )
    SearchWidgetEntry(
        date: Date().addingTimeInterval(300),
        searchProvider: .baidu,
        recentSearches: ["Widget", "搜索"],
        isHistoryEnabled: true
    )
}

#Preview("Medium Widget", as: .systemMedium) {
    SearchWidget()
} timeline: {
    SearchWidgetEntry(
        date: Date(),
        searchProvider: .google,
        recentSearches: ["iOS开发", "SwiftUI", "Widget"],
        isHistoryEnabled: true
    )
    SearchWidgetEntry(
        date: Date().addingTimeInterval(300),
        searchProvider: .duckduckgo,
        recentSearches: [],
        isHistoryEnabled: false
    )
}
