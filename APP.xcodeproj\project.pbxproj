// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		F40EEF7F2E3D3A6100DC95D9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F40EEF692E3D3A5D00DC95D9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F40EEF702E3D3A5D00DC95D9;
			remoteInfo = APP;
		};
		F40EEF892E3D3A6200DC95D9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F40EEF692E3D3A5D00DC95D9 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F40EEF702E3D3A5D00DC95D9;
			remoteInfo = APP;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		F40EEF712E3D3A5D00DC95D9 /* APP.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = APP.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F40EEF7E2E3D3A6100DC95D9 /* APPTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = APPTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F40EEF882E3D3A6200DC95D9 /* APPUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = APPUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		F40EEF732E3D3A5D00DC95D9 /* APP */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = APP;
			sourceTree = "<group>";
		};
		F40EEF812E3D3A6100DC95D9 /* APPTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = APPTests;
			sourceTree = "<group>";
		};
		F40EEF8B2E3D3A6200DC95D9 /* APPUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = APPUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		F40EEF6E2E3D3A5D00DC95D9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F40EEF7B2E3D3A6100DC95D9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F40EEF852E3D3A6200DC95D9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F40EEF682E3D3A5D00DC95D9 = {
			isa = PBXGroup;
			children = (
				F40EEF732E3D3A5D00DC95D9 /* APP */,
				F40EEF812E3D3A6100DC95D9 /* APPTests */,
				F40EEF8B2E3D3A6200DC95D9 /* APPUITests */,
				F40EEF722E3D3A5D00DC95D9 /* Products */,
			);
			sourceTree = "<group>";
		};
		F40EEF722E3D3A5D00DC95D9 /* Products */ = {
			isa = PBXGroup;
			children = (
				F40EEF712E3D3A5D00DC95D9 /* APP.app */,
				F40EEF7E2E3D3A6100DC95D9 /* APPTests.xctest */,
				F40EEF882E3D3A6200DC95D9 /* APPUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F40EEF702E3D3A5D00DC95D9 /* APP */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F40EEF922E3D3A6200DC95D9 /* Build configuration list for PBXNativeTarget "APP" */;
			buildPhases = (
				F40EEF6D2E3D3A5D00DC95D9 /* Sources */,
				F40EEF6E2E3D3A5D00DC95D9 /* Frameworks */,
				F40EEF6F2E3D3A5D00DC95D9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				F40EEF732E3D3A5D00DC95D9 /* APP */,
			);
			name = APP;
			packageProductDependencies = (
			);
			productName = APP;
			productReference = F40EEF712E3D3A5D00DC95D9 /* APP.app */;
			productType = "com.apple.product-type.application";
		};
		F40EEF7D2E3D3A6100DC95D9 /* APPTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F40EEF952E3D3A6200DC95D9 /* Build configuration list for PBXNativeTarget "APPTests" */;
			buildPhases = (
				F40EEF7A2E3D3A6100DC95D9 /* Sources */,
				F40EEF7B2E3D3A6100DC95D9 /* Frameworks */,
				F40EEF7C2E3D3A6100DC95D9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F40EEF802E3D3A6100DC95D9 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F40EEF812E3D3A6100DC95D9 /* APPTests */,
			);
			name = APPTests;
			packageProductDependencies = (
			);
			productName = APPTests;
			productReference = F40EEF7E2E3D3A6100DC95D9 /* APPTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F40EEF872E3D3A6200DC95D9 /* APPUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F40EEF982E3D3A6200DC95D9 /* Build configuration list for PBXNativeTarget "APPUITests" */;
			buildPhases = (
				F40EEF842E3D3A6200DC95D9 /* Sources */,
				F40EEF852E3D3A6200DC95D9 /* Frameworks */,
				F40EEF862E3D3A6200DC95D9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F40EEF8A2E3D3A6200DC95D9 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F40EEF8B2E3D3A6200DC95D9 /* APPUITests */,
			);
			name = APPUITests;
			packageProductDependencies = (
			);
			productName = APPUITests;
			productReference = F40EEF882E3D3A6200DC95D9 /* APPUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F40EEF692E3D3A5D00DC95D9 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					F40EEF702E3D3A5D00DC95D9 = {
						CreatedOnToolsVersion = 16.4;
					};
					F40EEF7D2E3D3A6100DC95D9 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = F40EEF702E3D3A5D00DC95D9;
					};
					F40EEF872E3D3A6200DC95D9 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = F40EEF702E3D3A5D00DC95D9;
					};
				};
			};
			buildConfigurationList = F40EEF6C2E3D3A5D00DC95D9 /* Build configuration list for PBXProject "APP" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F40EEF682E3D3A5D00DC95D9;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = F40EEF722E3D3A5D00DC95D9 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F40EEF702E3D3A5D00DC95D9 /* APP */,
				F40EEF7D2E3D3A6100DC95D9 /* APPTests */,
				F40EEF872E3D3A6200DC95D9 /* APPUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F40EEF6F2E3D3A5D00DC95D9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F40EEF7C2E3D3A6100DC95D9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F40EEF862E3D3A6200DC95D9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F40EEF6D2E3D3A5D00DC95D9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F40EEF7A2E3D3A6100DC95D9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F40EEF842E3D3A6200DC95D9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F40EEF802E3D3A6100DC95D9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F40EEF702E3D3A5D00DC95D9 /* APP */;
			targetProxy = F40EEF7F2E3D3A6100DC95D9 /* PBXContainerItemProxy */;
		};
		F40EEF8A2E3D3A6200DC95D9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F40EEF702E3D3A5D00DC95D9 /* APP */;
			targetProxy = F40EEF892E3D3A6200DC95D9 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		F40EEF902E3D3A6200DC95D9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F40EEF912E3D3A6200DC95D9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F40EEF932E3D3A6200DC95D9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.label.APP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F40EEF942E3D3A6200DC95D9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.label.APP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F40EEF962E3D3A6200DC95D9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.label.APPTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/APP.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/APP";
			};
			name = Debug;
		};
		F40EEF972E3D3A6200DC95D9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.label.APPTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/APP.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/APP";
			};
			name = Release;
		};
		F40EEF992E3D3A6200DC95D9 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.label.APPUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = APP;
			};
			name = Debug;
		};
		F40EEF9A2E3D3A6200DC95D9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.label.APPUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = APP;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F40EEF6C2E3D3A5D00DC95D9 /* Build configuration list for PBXProject "APP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F40EEF902E3D3A6200DC95D9 /* Debug */,
				F40EEF912E3D3A6200DC95D9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F40EEF922E3D3A6200DC95D9 /* Build configuration list for PBXNativeTarget "APP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F40EEF932E3D3A6200DC95D9 /* Debug */,
				F40EEF942E3D3A6200DC95D9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F40EEF952E3D3A6200DC95D9 /* Build configuration list for PBXNativeTarget "APPTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F40EEF962E3D3A6200DC95D9 /* Debug */,
				F40EEF972E3D3A6200DC95D9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F40EEF982E3D3A6200DC95D9 /* Build configuration list for PBXNativeTarget "APPUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F40EEF992E3D3A6200DC95D9 /* Debug */,
				F40EEF9A2E3D3A6200DC95D9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F40EEF692E3D3A5D00DC95D9 /* Project object */;
}
