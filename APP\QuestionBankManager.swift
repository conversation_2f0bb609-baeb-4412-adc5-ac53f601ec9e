//
//  QuestionBankManager.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import SwiftUI

class QuestionBankManager: ObservableObject {
    @Published var questionBanks: [QuestionBank] = []
    @Published var searchHistory: [String] = []
    
    private let documentsDirectory: URL
    private let questionBanksFileName = "questionBanks.json"
    private let searchHistoryKey = "searchHistory"
    
    init() {
        documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        loadQuestionBanks()
        loadSearchHistory()
    }
    
    // MARK: - Question Bank Management
    func addQuestionBank(_ questionBank: QuestionBank) {
        questionBanks.append(questionBank)
        saveQuestionBanks()
    }
    
    func removeQuestionBank(at index: Int) {
        guard index < questionBanks.count else { return }
        questionBanks.remove(at: index)
        saveQuestionBanks()
    }
    
    func updateQuestionBank(_ questionBank: QuestionBank) {
        if let index = questionBanks.firstIndex(where: { $0.id == questionBank.id }) {
            questionBanks[index] = questionBank
            saveQuestionBanks()
        }
    }
    
    func renameQuestionBank(at index: Int, newName: String) {
        guard index < questionBanks.count else { return }
        questionBanks[index].updateName(newName)
        saveQuestionBanks()
    }
    
    // MARK: - Search Functionality
    func searchQuestions(keyword: String, in bankIndex: Int? = nil) -> [(bankName: String, questions: [Question])] {
        guard !keyword.isEmpty else { return [] }
        
        var results: [(bankName: String, questions: [Question])] = []
        
        if let bankIndex = bankIndex, bankIndex < questionBanks.count {
            // 在指定题库中搜索
            let bank = questionBanks[bankIndex]
            let foundQuestions = bank.searchQuestions(keyword: keyword)
            if !foundQuestions.isEmpty {
                results.append((bankName: bank.name, questions: foundQuestions))
            }
        } else {
            // 在所有题库中搜索
            for bank in questionBanks {
                let foundQuestions = bank.searchQuestions(keyword: keyword)
                if !foundQuestions.isEmpty {
                    results.append((bankName: bank.name, questions: foundQuestions))
                }
            }
        }
        
        // 保存搜索历史
        saveSearchHistory(keyword)
        
        return results
    }
    
    // MARK: - File Import
    func importQuestionBank(from url: URL, name: String) throws {
        guard url.startAccessingSecurityScopedResource() else {
            throw ImportError.accessDenied
        }
        defer { url.stopAccessingSecurityScopedResource() }
        
        let content = try String(contentsOf: url, encoding: .utf8)
        let questions = TXTParser.parseQuestions(from: content)
        
        guard !questions.isEmpty else {
            throw ImportError.noQuestionsFound
        }
        
        let questionBank = QuestionBank(name: name, questions: questions)
        addQuestionBank(questionBank)
    }
    
    // MARK: - Search History
    private func saveSearchHistory(_ query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return }
        
        // Remove if already exists
        searchHistory.removeAll { $0 == trimmedQuery }
        
        // Insert at beginning
        searchHistory.insert(trimmedQuery, at: 0)
        
        // Keep only recent 20 items
        searchHistory = Array(searchHistory.prefix(20))
        
        // Save to UserDefaults
        UserDefaults.standard.set(searchHistory, forKey: searchHistoryKey)
    }
    
    private func loadSearchHistory() {
        searchHistory = UserDefaults.standard.stringArray(forKey: searchHistoryKey) ?? []
    }
    
    func clearSearchHistory() {
        searchHistory.removeAll()
        UserDefaults.standard.removeObject(forKey: searchHistoryKey)
    }
    
    // MARK: - Data Persistence
    private func saveQuestionBanks() {
        do {
            let data = try JSONEncoder().encode(questionBanks)
            let url = documentsDirectory.appendingPathComponent(questionBanksFileName)
            try data.write(to: url)
        } catch {
            print("Failed to save question banks: \(error)")
        }
    }
    
    private func loadQuestionBanks() {
        let url = documentsDirectory.appendingPathComponent(questionBanksFileName)
        
        do {
            let data = try Data(contentsOf: url)
            questionBanks = try JSONDecoder().decode([QuestionBank].self, from: data)
        } catch {
            print("Failed to load question banks: \(error)")
            questionBanks = []
        }
    }
}

// MARK: - Import Errors
enum ImportError: LocalizedError {
    case accessDenied
    case noQuestionsFound
    case invalidFormat
    
    var errorDescription: String? {
        switch self {
        case .accessDenied:
            return "无法访问文件"
        case .noQuestionsFound:
            return "文件中未找到有效题目"
        case .invalidFormat:
            return "文件格式不正确"
        }
    }
}
