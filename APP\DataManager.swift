//
//  DataManager.swift
//  APP
//
//  Created by tongsy on 2025/8/2.
//

import Foundation
import SwiftUI

class DataManager: ObservableObject {
    private let userDefaults: UserDefaults

    // App Group identifier for sharing data with Widget
    static let appGroupIdentifier = "group.com.label.stealthsearch"

    init() {
        // Try to use App Group UserDefaults, fallback to standard if not available
        if let groupDefaults = UserDefaults(suiteName: DataManager.appGroupIdentifier) {
            self.userDefaults = groupDefaults
        } else {
            self.userDefaults = UserDefaults.standard
        }
        loadSettings()
    }
    
    // MARK: - Keys
    private enum Keys {
        static let searchHistory = "searchHistory"
        static let searchProvider = "searchProvider"
        static let maxHistoryCount = "maxHistoryCount"
        static let enableHistory = "enableHistory"
    }
    
    // MARK: - Published Properties
    @Published var searchHistory: [String] = []
    @Published var selectedProvider: SearchEngine.SearchProvider = .google
    @Published var enableHistory: Bool = true
    

    
    // MARK: - Search History Management
    func saveSearchHistory(_ query: String) {
        guard enableHistory && !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        var history = getSearchHistory()
        
        // Remove if already exists to avoid duplicates
        history.removeAll { $0 == query }
        
        // Insert at beginning
        history.insert(query, at: 0)
        
        // Keep only recent items
        let maxCount = getMaxHistoryCount()
        history = Array(history.prefix(maxCount))
        
        userDefaults.set(history, forKey: Keys.searchHistory)
        
        // Update published property
        DispatchQueue.main.async {
            self.searchHistory = history
        }
    }
    
    func getSearchHistory() -> [String] {
        return userDefaults.stringArray(forKey: Keys.searchHistory) ?? []
    }
    
    func clearSearchHistory() {
        userDefaults.removeObject(forKey: Keys.searchHistory)
        DispatchQueue.main.async {
            self.searchHistory = []
        }
    }
    
    func removeSearchHistoryItem(_ query: String) {
        var history = getSearchHistory()
        history.removeAll { $0 == query }
        userDefaults.set(history, forKey: Keys.searchHistory)
        
        DispatchQueue.main.async {
            self.searchHistory = history
        }
    }
    
    // MARK: - Search Provider Management
    func saveSearchProvider(_ provider: SearchEngine.SearchProvider) {
        userDefaults.set(provider.rawValue, forKey: Keys.searchProvider)
        DispatchQueue.main.async {
            self.selectedProvider = provider
        }
    }
    
    func getSearchProvider() -> SearchEngine.SearchProvider {
        let providerString = userDefaults.string(forKey: Keys.searchProvider) ?? SearchEngine.SearchProvider.google.rawValue
        return SearchEngine.SearchProvider(rawValue: providerString) ?? .google
    }
    
    // MARK: - Settings Management
    func getMaxHistoryCount() -> Int {
        let count = userDefaults.integer(forKey: Keys.maxHistoryCount)
        return count > 0 ? count : 20 // Default to 20
    }
    
    func setMaxHistoryCount(_ count: Int) {
        userDefaults.set(count, forKey: Keys.maxHistoryCount)
    }
    
    func getEnableHistory() -> Bool {
        return userDefaults.bool(forKey: Keys.enableHistory)
    }
    
    func setEnableHistory(_ enabled: Bool) {
        userDefaults.set(enabled, forKey: Keys.enableHistory)
        DispatchQueue.main.async {
            self.enableHistory = enabled
        }
    }
    
    // MARK: - Load Settings
    private func loadSettings() {
        searchHistory = getSearchHistory()
        selectedProvider = getSearchProvider()
        enableHistory = getEnableHistory()
    }
}
